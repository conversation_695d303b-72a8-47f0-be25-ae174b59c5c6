<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Controller\Category;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\UrlInterface;

class Index implements ActionInterface
{
    private const ERROR_MESSAGE = 'Category management is restricted to administrators only.';
    private const SELLER_DASHBOARD_PATH = 'marketplace/account/dashboard';

    public function __construct(
        private readonly ResultFactory $resultFactory,
        private readonly ManagerInterface $messageManager,
        private readonly UrlInterface $url
    ) {}

    public function execute(): Redirect
    {
        $this->messageManager->addErrorMessage(__(self::ERROR_MESSAGE));

        $result = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $result->setUrl($this->url->getUrl(self::SELLER_DASHBOARD_PATH));

        return $result;
    }
}
