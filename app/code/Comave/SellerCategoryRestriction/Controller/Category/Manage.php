<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Controller\Category;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\Result\Forward;
use Magento\Framework\Controller\ResultFactory;

/**
 * Catch-all controller for disabled seller category routes
 */
class NoRoute implements ActionInterface
{
    public function __construct(
        private readonly ResultFactory $resultFactory
    ) {}

    public function execute(): Forward
    {
        // Return 404 for all seller category management requests
        $result = $this->resultFactory->create(ResultFactory::TYPE_FORWARD);
        $result->forward('noroute');
        
        return $result;
    }
}
