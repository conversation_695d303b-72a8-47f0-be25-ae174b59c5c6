<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Controller\Category;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\Result\Forward;

/**
 * Block access to seller category creation
 */
class NewAction implements ActionInterface
{
    public function __construct(
        private readonly ResultFactory $resultFactory
    ) {}

    public function execute(): Forward
    {
        /** @var Forward $result */
        $result = $this->resultFactory->create(ResultFactory::TYPE_FORWARD);
        $result->setController('noroute');
        $result->forward('index');

        return $result;
    }
}
