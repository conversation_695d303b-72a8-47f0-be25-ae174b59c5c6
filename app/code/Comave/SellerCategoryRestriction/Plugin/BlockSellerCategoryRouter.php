<?php
declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Plugin;

use Magento\Framework\App\Request\Http;

class BlockSellerCategoryRequests
{
    /**
     * Intercept request to block mpsellercategory URLs
     *
     * @param Http $subject
     * @param string $pathInfo
     * @return string
     */
    public function afterGetPathInfo(Http $subject, $pathInfo)
    {
        // If this is a seller category request, redirect to noroute
        if ($pathInfo && strpos($pathInfo, '/mpsellercategory/') === 0) {
            return '/noroute';
        }
        
        return $pathInfo;
    }
}
