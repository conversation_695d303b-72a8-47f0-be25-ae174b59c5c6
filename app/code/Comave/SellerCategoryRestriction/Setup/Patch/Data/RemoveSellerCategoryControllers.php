<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class RemoveSellerCategoryControllers implements DataPatchInterface
{
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup
    ) {}

    public function apply(): void
    {
        $this->moduleDataSetup->startSetup();

        // Create URL rewrites to redirect seller category URLs to dashboard
        $connection = $this->moduleDataSetup->getConnection();
        $urlRewriteTable = $this->moduleDataSetup->getTable('url_rewrite');

        $categoryUrls = [
            'mpsellercategory/category/manage',
            'mpsellercategory/category/save',
            'mpsellercategory/category/edit',
            'mpsellercategory/category/new',
            'mpsellercategory/category/delete'
        ];

        foreach ($categoryUrls as $requestPath) {
            // Check if rewrite already exists
            $existingRewrite = $connection->fetchOne(
                $connection->select()
                    ->from($urlRewriteTable, 'url_rewrite_id')
                    ->where('request_path = ?', $requestPath)
            );

            if (!$existingRewrite) {
                $connection->insert($urlRewriteTable, [
                    'entity_type' => 'custom',
                    'entity_id' => 0,
                    'request_path' => $requestPath,
                    'target_path' => 'marketplace/account/dashboard',
                    'redirect_type' => 301,
                    'store_id' => 0,
                    'description' => 'Redirect seller category management to dashboard',
                    'is_autogenerated' => 0
                ]);
            }
        }

        // Also remove any existing controller entries from marketplace_controller_list
        $controllerTable = $this->moduleDataSetup->getTable('marketplace_controller_list');
        $connection->delete(
            $controllerTable,
            ['controller_path LIKE ?' => 'mpsellercategory/category/%']
        );

        $this->moduleDataSetup->endSetup();
    }
}
